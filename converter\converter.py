"""
单位转换工具 - PySide6现代化版本
基于spliced_pictures_qt.py的设计风格，实现UI美化和功能迁移
支持字节单位转换、时间戳转换、货币汇率转换
"""

import sys
import time
import datetime
import requests  # 用于获取实时汇率
from pathlib import Path

from PySide6.QtCore import Qt, QTimer
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
    QLabel, QSplitter, QTabWidget, QTextEdit, QLineEdit, QComboBox,
    QMessageBox
)
from PySide6.QtGui import QGuiApplication, QFont

# 尝试导入BeautifulSoup，如果不可用，记录错误
try:
    from bs4 import BeautifulSoup
    BS4_AVAILABLE = True
except ImportError:
    BS4_AVAILABLE = False

# 复用spliced_pictures_qt.py的现代化样式表
STYLE_SHEET = """
QWidget {
    font-family: 'Microsoft YaHei', 'Segoe UI', 'SF Pro Display', Arial, sans-serif;
    font-size: 9pt;
    color: #2c3e50;
    selection-background-color: #e3f2fd;
}

QMainWindow {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #f8f9fa, stop:1 #e9ecef);
}

QPushButton {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #1a73e8, stop:1 #1557b0);
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 5px;
    font-weight: 500;
    min-width: 60px;
    min-height: 26px;
    font-size: 9pt;
}

QPushButton:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #1557b0, stop:1 #0d47a1);
}

QPushButton:pressed {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #0d47a1, stop:1 #1557b0);
}

QPushButton:disabled {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #e9ecef, stop:1 #dee2e6);
    color: #adb5bd;
    border: 1px solid #dee2e6;
}

QLineEdit {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #ffffff, stop:1 #f8f9fa);
    border: 2px solid #dee2e6;
    border-radius: 4px;
    padding: 4px 8px;
    min-height: 20px;
    font-size: 9pt;
    color: #495057;
}

QLineEdit:hover {
    border: 2px solid #2196f3;
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #f8f9fa, stop:1 #e9ecef);
}

QLineEdit:focus {
    border: 2px solid #1a73e8;
    background: white;
}

QComboBox {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #ffffff, stop:1 #f8f9fa);
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 4px 8px;
    min-width: 80px;
    min-height: 20px;
    font-size: 9pt;
    color: #495057;
}

QComboBox:hover {
    border: 1px solid #2196f3;
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #f8f9fa, stop:1 #e9ecef);
}

QComboBox:focus {
    border: 2px solid #1a73e8;
    background: white;
}

QTextEdit {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #ffffff, stop:1 #f8f9fa);
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 8px;
    font-size: 10pt;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
}

QTabWidget::pane {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #ffffff, stop:1 #f8f9fa);
}

QTabBar::tab {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #f8f9fa, stop:1 #e9ecef);
    border: 1px solid #dee2e6;
    padding: 8px 16px;
    margin-right: 2px;
    border-top-left-radius: 6px;
    border-top-right-radius: 6px;
}

QTabBar::tab:selected {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #1a73e8, stop:1 #1557b0);
    color: white;
    border-bottom: none;
}

QTabBar::tab:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #e3f2fd, stop:1 #bbdefb);
}

QSplitter::handle {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
        stop:0 #dee2e6, stop:0.5 #adb5bd, stop:1 #dee2e6);
}

QSplitter::handle:horizontal {
    width: 3px;
}

QSplitter::handle:hover {
    background: #2196f3;
}

QLabel {
    color: #495057;
}

QStatusBar {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #f8f9fa, stop:1 #e9ecef);
    border-top: 1px solid #dee2e6;
    padding: 4px;
}
"""


class ConverterApp(QMainWindow):
    """
    现代化单位转换工具 - PySide6版本
    支持:
    - 字节单位转换 (B, KB, MB, GB, TB, PB)
    - 时间戳转换
    - 货币汇率转换
    """

    def __init__(self):
        super().__init__()
        self.setWindowTitle("🔧 单位转换工具")

        # 设置窗口大小和居中显示
        self.setup_window()

        # Unit conversion factors (powers of 1024)
        self.units = {
            "字节 (B)": 0,
            "千字节 (KB)": 1,
            "兆字节 (MB)": 2,
            "千兆字节 (GB)": 3,
            "太字节 (TB)": 4,
            "拍字节 (PB)": 5
        }

        # 初始化汇率相关变量
        self.exchange_rate = 7.2  # 默认汇率：1美元 = 7.2人民币
        self.last_rate_update = None  # 上次汇率更新时间

        # 创建主界面
        self.setup_ui()

        # 应用现代化样式
        self.setStyleSheet(STYLE_SHEET)

    def setup_window(self):
        """设置窗口属性"""
        window_width = 1000
        window_height = 800

        # 获取屏幕几何信息
        screen = QGuiApplication.primaryScreen().geometry()

        # 计算居中位置
        x = (screen.width() - window_width) // 2
        y = (screen.height() - window_height) // 2

        self.setGeometry(x, y, window_width, window_height)
        self.setMinimumSize(900, 700)




    def setup_ui(self):
        """设置现代化的用户界面 - 左右分栏布局"""
        # 创建中央组件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建主布局
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)

        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)

        # 左侧功能选择面板
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        left_layout.setContentsMargins(0, 0, 0, 0)

        # 左侧面板标题
        title_label = QLabel("🔧 单位转换设置")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #2c3e50; margin-bottom: 10px;")
        left_layout.addWidget(title_label)

        # 创建选项卡组件
        self.tab_widget = QTabWidget()
        left_layout.addWidget(self.tab_widget)

        # 添加三个选项卡
        self.byte_tab = QWidget()
        self.timestamp_tab = QWidget()
        self.currency_tab = QWidget()

        self.tab_widget.addTab(self.byte_tab, "📊 字节转换")
        self.tab_widget.addTab(self.timestamp_tab, "⏰ 时间戳")
        self.tab_widget.addTab(self.currency_tab, "💰 货币汇率")

        # 右侧结果展示面板
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        right_layout.setContentsMargins(0, 0, 0, 0)

        # 右侧面板标题
        result_title = QLabel("📋 转换结果")
        result_title.setStyleSheet("font-size: 18px; font-weight: bold; color: #2c3e50; margin-bottom: 10px;")
        right_layout.addWidget(result_title)

        # 结果展示区域
        self.result_display = QTextEdit()
        self.result_display.setReadOnly(True)
        self.result_display.setPlaceholderText("转换结果将在此显示...")
        right_layout.addWidget(self.result_display)

        # 添加到分割器
        splitter.addWidget(left_widget)
        splitter.addWidget(right_widget)
        splitter.setSizes([400, 600])  # 设置初始比例

        # 设置各个转换功能的UI
        self.setup_byte_converter_ui()
        self.setup_timestamp_converter_ui()
        self.setup_currency_converter_ui()

        # 创建状态栏
        self.statusBar().showMessage("🟢 准备就绪")

    def setup_byte_converter_ui(self):
        """设置字节转换UI - 使用PySide6组件"""
        layout = QVBoxLayout(self.byte_tab)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)

        # 输入区域标题
        title_label = QLabel("📊 字节单位转换")
        title_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #2c3e50; margin-bottom: 10px;")
        layout.addWidget(title_label)

        # 数值输入区域
        value_label = QLabel("输入数值:")
        layout.addWidget(value_label)

        self.byte_entry = QLineEdit()
        self.byte_entry.setPlaceholderText("请输入数值...")
        layout.addWidget(self.byte_entry)

        # 单位选择区域
        unit_label = QLabel("选择单位:")
        layout.addWidget(unit_label)

        self.byte_unit_combo = QComboBox()
        self.byte_unit_combo.addItems(list(self.units.keys()))
        self.byte_unit_combo.setCurrentIndex(0)
        layout.addWidget(self.byte_unit_combo)

        # 转换按钮
        self.byte_convert_btn = QPushButton("🔄 开始转换")
        self.byte_convert_btn.clicked.connect(self.convert_bytes)
        layout.addWidget(self.byte_convert_btn)

        # 添加弹性空间
        layout.addStretch()

        # 绑定事件
        self.byte_entry.returnPressed.connect(self.convert_bytes)
        self.byte_entry.textChanged.connect(self.validate_byte_input)
        self.byte_unit_combo.currentTextChanged.connect(lambda: self.convert_bytes())

    def setup_timestamp_converter_ui(self):
        """设置时间戳转换UI - 使用PySide6组件"""
        layout = QVBoxLayout(self.timestamp_tab)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)

        # 输入区域标题
        title_label = QLabel("⏰ 时间戳转换")
        title_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #2c3e50; margin-bottom: 10px;")
        layout.addWidget(title_label)

        # 时间输入区域
        input_label = QLabel("输入时间戳/时间:")
        layout.addWidget(input_label)

        self.timestamp_entry = QLineEdit()
        self.timestamp_entry.setPlaceholderText("请输入时间戳或时间...")
        layout.addWidget(self.timestamp_entry)

        # 转换类型选择
        type_label = QLabel("转换类型:")
        layout.addWidget(type_label)

        self.timestamp_type_combo = QComboBox()
        self.timestamp_type_combo.addItems(["时间戳转时间", "时间转时间戳"])
        self.timestamp_type_combo.setCurrentIndex(0)
        layout.addWidget(self.timestamp_type_combo)

        # 按钮区域
        button_layout = QHBoxLayout()

        # 当前时间按钮
        self.now_button = QPushButton("📅 当前时间")
        self.now_button.clicked.connect(self.set_current_time)
        button_layout.addWidget(self.now_button)

        # 转换按钮
        self.timestamp_convert_btn = QPushButton("🔄 开始转换")
        self.timestamp_convert_btn.clicked.connect(self.convert_timestamp)
        button_layout.addWidget(self.timestamp_convert_btn)

        layout.addLayout(button_layout)

        # 添加弹性空间
        layout.addStretch()

        # 绑定事件
        self.timestamp_entry.returnPressed.connect(self.convert_timestamp)
        self.timestamp_entry.textChanged.connect(self.validate_timestamp_input)
        self.timestamp_type_combo.currentTextChanged.connect(self.update_timestamp_placeholder)
        self.update_timestamp_placeholder()

    def validate_byte_input(self):
        """Validate that the input is a valid number for byte conversion"""
        value = self.byte_value_var.get().strip()
        if not value:
            return

        try:
            # Allow for decimal numbers
            float(value)
            self.status_var.set("准备就绪")
        except ValueError:
            self.status_var.set("请输入有效的数字")

    def validate_timestamp_input(self):
        """Validate that the input is a valid timestamp"""
        value = self.timestamp_value_var.get().strip()
        if not value:
            return

        try:
            # Allow for integer or decimal numbers
            float(value)
            self.status_var.set("准备就绪")
        except ValueError:
            self.status_var.set("请输入有效的时间戳")

    def format_bytes_value(self, value):
        """Format byte values for better display"""
        if value == 0:
            return "0"

        # 对于非常大的数字使用科学计数法
        if abs(value) >= 1e15:
            return f"{value:.2e}"

        # 对于非常小的数字使用科学计数法
        if abs(value) < 1e-6 and value != 0:
            return f"{value:.2e}"

        # 对于整数，直接显示
        if value == int(value):
            return f"{int(value):,}"

        # 对于小数，根据大小确定精度
        if abs(value) >= 1000:
            return f"{value:,.2f}"
        elif abs(value) >= 1:
            return f"{value:.4f}"
        else:
            return f"{value:.6f}".rstrip('0').rstrip('.')

    def get_relative_time(self, timestamp):
        """Get relative time description"""
        try:
            now = time.time()
            diff = now - timestamp

            if abs(diff) < 60:
                return "刚刚"
            elif abs(diff) < 3600:
                minutes = int(abs(diff) / 60)
                return f"{minutes}分钟{'前' if diff > 0 else '后'}"
            elif abs(diff) < 86400:
                hours = int(abs(diff) / 3600)
                return f"{hours}小时{'前' if diff > 0 else '后'}"
            elif abs(diff) < 2592000:  # 30 days
                days = int(abs(diff) / 86400)
                return f"{days}天{'前' if diff > 0 else '后'}"
            elif abs(diff) < 31536000:  # 365 days
                months = int(abs(diff) / 2592000)
                return f"{months}个月{'前' if diff > 0 else '后'}"
            else:
                years = int(abs(diff) / 31536000)
                return f"{years}年{'前' if diff > 0 else '后'}"
        except:
            return "无法计算"

    def convert_bytes(self):
        """Convert the input value to all byte units"""
        value = self.byte_value_var.get().strip()
        if not value:
            return

        try:
            # Get the numeric value and source unit
            numeric_value = float(value)
            source_unit = self.byte_unit_var.get()
            source_power = self.units[source_unit]

            # Convert to bytes first (base unit)
            bytes_value = numeric_value * (1024 ** source_power)

            # 构建结果文本
            result_text = f"📊 字节单位转换结果\n\n"
            result_text += f"输入: {self.format_bytes_value(numeric_value)} {source_unit}\n\n"
            result_text += "转换结果:\n"
            result_text += "=" * 40 + "\n"

            # Convert to all units and format for display
            for unit, power in self.units.items():
                result = bytes_value / (1024 ** power)
                formatted_result = self.format_bytes_value(result)

                # 高亮显示源单位
                if unit == source_unit:
                    result_text += f"➤ {unit}: {formatted_result} ⭐\n"
                else:
                    result_text += f"  {unit}: {formatted_result}\n"

            # 添加额外信息
            result_text += "\n" + "=" * 40 + "\n"
            result_text += f"基础字节数: {self.format_bytes_value(bytes_value)} Bytes\n"

            # 更新右侧结果展示面板
            self.result_display.delete("1.0", "end")
            self.result_display.insert("1.0", result_text)

            self.status_var.set(f"✅ 已将 {self.format_bytes_value(numeric_value)} {source_unit} 转换为所有单位")

        except ValueError:
            self.status_var.set("❌ 请输入有效的数字")
        except Exception as e:
            self.status_var.set(f"❌ 转换错误: {str(e)}")

    def convert_timestamp(self):
        """Convert the timestamp to human-readable date and time or vice versa"""
        value = self.timestamp_value_var.get().strip()
        if not value:
            return

        try:
            # 获取转换类型
            conversion_type = self.timestamp_type_var.get()

            if conversion_type == "时间戳转时间":
                # 时间戳转时间
                timestamp_value = float(value)

                # 转换为datetime对象
                dt = datetime.datetime.fromtimestamp(timestamp_value)

                # 主要结果 - 标准格式
                main_result = dt.strftime("%Y-%m-%d %H:%M:%S")

                # 详细信息 - 中文格式和星期
                weekdays = ["星期一", "星期二", "星期三", "星期四", "星期五", "星期六", "星期日"]
                weekday = weekdays[dt.weekday()]
                detail_result = f"{dt.year}年{dt.month:02d}月{dt.day:02d}日 {weekday} {dt.hour:02d}时{dt.minute:02d}分{dt.second:02d}秒"

                # ISO格式
                iso_result = dt.isoformat()

                # 相对时间
                relative_result = self.get_relative_time(timestamp_value)

                # 更新状态栏
                self.status_var.set(f"已将时间戳 {value} 转换为日期时间")

            else:
                # 时间转时间戳
                try:
                    # 尝试解析时间字符串
                    dt = datetime.datetime.strptime(value, "%Y-%m-%d %H:%M:%S")
                except ValueError:
                    try:
                        # 尝试其他常见格式
                        dt = datetime.datetime.strptime(value, "%Y/%m/%d %H:%M:%S")
                    except ValueError:
                        try:
                            dt = datetime.datetime.strptime(value, "%Y-%m-%d")
                        except ValueError:
                            raise ValueError("无法解析时间格式，请使用 YYYY-MM-DD HH:MM:SS 格式")

                # 获取时间戳
                timestamp = dt.timestamp()

                # 主要结果 - Unix时间戳
                main_result = f"Unix时间戳: {int(timestamp)} 秒"

                # 详细信息 - 毫秒时间戳
                detail_result = f"毫秒时间戳: {int(timestamp * 1000)} 毫秒"

                # ISO格式
                iso_result = dt.isoformat()

                # 相对时间
                relative_result = self.get_relative_time(timestamp)

                # 更新状态栏
                self.status_var.set(f"已将日期时间 {value} 转换为时间戳")

            # 构建结果文本
            result_text = f"⏰ 时间戳转换结果\n\n"
            result_text += f"输入: {value}\n"
            result_text += f"转换类型: {conversion_type}\n\n"
            result_text += "转换结果:\n"
            result_text += "=" * 40 + "\n"
            result_text += f"📅 主要结果:\n{main_result}\n\n"
            result_text += f"📝 详细信息:\n{detail_result}\n\n"
            result_text += f"🌐 ISO格式:\n{iso_result}\n\n"
            result_text += f"⏱️ 相对时间:\n{relative_result}\n"

            # 更新右侧结果展示面板
            self.result_display.delete("1.0", "end")
            self.result_display.insert("1.0", result_text)

            # 更新结果变量（保持兼容性）
            self.timestamp_results['main'].set(main_result)
            self.timestamp_results['detail'].set(detail_result)
            self.timestamp_results['iso'].set(iso_result)
            self.timestamp_results['relative'].set(relative_result)

        except ValueError as e:
            if "无法解析时间格式" in str(e):
                self.status_var.set(f"❌ {str(e)}")
            else:
                self.status_var.set("❌ 请输入有效的时间戳或时间格式")
        except Exception as e:
            self.status_var.set(f"❌ 转换错误: {str(e)}")

    def update_timestamp_placeholder(self):
        """Update the placeholder text based on the selected conversion type"""
        conversion_type = self.timestamp_type_var.get()
        if conversion_type == "时间戳转时间":
            # 清除当前输入
            self.timestamp_entry.delete(0, tk.END)
            # 清除所有结果
            for key in self.timestamp_results:
                self.timestamp_results[key].set("")
            # 设置状态栏文本
            if hasattr(self, 'status_var'):
                self.status_var.set("请输入Unix时间戳值，例如: 1609459200")
        else:
            # 清除当前输入
            self.timestamp_entry.delete(0, tk.END)
            # 清除所有结果
            for key in self.timestamp_results:
                self.timestamp_results[key].set("")
            # 设置状态栏文本
            if hasattr(self, 'status_var'):
                self.status_var.set("请输入时间，例如: 2021-01-01 00:00:00")

    def set_current_time(self):
        """Set current time or timestamp based on the conversion type"""
        # 获取转换类型
        conversion_type = self.timestamp_type_var.get()

        if conversion_type == "时间转时间戳":
            # 如果是时间转时间戳，设置当前时间
            now = datetime.datetime.now()
            self.timestamp_value_var.set(now.strftime("%Y-%m-%d %H:%M:%S"))
        else:
            # 如果是时间戳转时间，设置当前时间戳
            current_timestamp = time.time()
            self.timestamp_value_var.set(str(int(current_timestamp)))

        # 转换
        self.convert_timestamp()

    def setup_currency_converter_ui(self):
        """设置货币转换UI - 使用PySide6组件"""
        layout = QVBoxLayout(self.currency_tab)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)

        # 输入区域标题
        title_label = QLabel("💰 货币汇率转换")
        title_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #2c3e50; margin-bottom: 10px;")
        layout.addWidget(title_label)

        # 美元金额输入区域
        usd_label = QLabel("输入美元金额:")
        layout.addWidget(usd_label)

        self.usd_entry = QLineEdit()
        self.usd_entry.setPlaceholderText("请输入美元金额...")
        layout.addWidget(self.usd_entry)

        # 汇率设置区域
        rate_label = QLabel("当前汇率 (人民币/美元):")
        layout.addWidget(rate_label)

        self.rate_entry = QLineEdit()
        self.rate_entry.setText(str(self.exchange_rate))
        layout.addWidget(self.rate_entry)

        # 更新时间显示
        self.update_time_label = QLabel("🕒 尚未更新汇率")
        self.update_time_label.setStyleSheet("color: gray; font-size: 9pt;")
        layout.addWidget(self.update_time_label)

        # 按钮区域
        button_layout = QHBoxLayout()

        # 更新汇率按钮
        self.update_rate_btn = QPushButton("🔄 更新汇率")
        self.update_rate_btn.clicked.connect(self.get_exchange_rate)
        button_layout.addWidget(self.update_rate_btn)

        # 转换按钮
        self.currency_convert_btn = QPushButton("💱 开始转换")
        self.currency_convert_btn.clicked.connect(self.convert_currency)
        button_layout.addWidget(self.currency_convert_btn)

        layout.addLayout(button_layout)

        # 添加弹性空间
        layout.addStretch()

        # 绑定事件
        self.usd_entry.returnPressed.connect(self.convert_currency)
        self.usd_entry.textChanged.connect(self.validate_currency_input)
        self.rate_entry.textChanged.connect(self.update_manual_rate)

    def validate_byte_input(self):
        """验证字节输入是否为有效数字"""
        value = self.byte_entry.text().strip()
        if not value:
            return

        try:
            # 允许小数输入
            float(value)
            self.statusBar().showMessage("🟢 准备就绪")
        except ValueError:
            self.statusBar().showMessage("⚠️ 请输入有效的数字")

    def validate_timestamp_input(self):
        """验证时间戳输入是否有效"""
        value = self.timestamp_entry.text().strip()
        if not value:
            return

        try:
            # 允许整数或小数
            float(value)
            self.statusBar().showMessage("🟢 准备就绪")
        except ValueError:
            self.statusBar().showMessage("⚠️ 请输入有效的时间戳")

    def format_bytes_value(self, value):
        """格式化字节值以便更好地显示"""
        if value == 0:
            return "0"

        # 对于非常大的数字使用科学计数法
        if abs(value) >= 1e15:
            return f"{value:.2e}"

        # 对于非常小的数字使用科学计数法
        if abs(value) < 1e-6 and value != 0:
            return f"{value:.2e}"

        # 对于整数，直接显示
        if value == int(value):
            return f"{int(value):,}"

        # 对于小数，根据大小确定精度
        if abs(value) >= 1000:
            return f"{value:,.2f}"
        elif abs(value) >= 1:
            return f"{value:.4f}"
        else:
            return f"{value:.6f}".rstrip('0').rstrip('.')

    def get_relative_time(self, timestamp):
        """获取相对时间描述"""
        try:
            now = time.time()
            diff = now - timestamp

            if abs(diff) < 60:
                return "刚刚"
            elif abs(diff) < 3600:
                minutes = int(abs(diff) / 60)
                return f"{minutes}分钟{'前' if diff > 0 else '后'}"
            elif abs(diff) < 86400:
                hours = int(abs(diff) / 3600)
                return f"{hours}小时{'前' if diff > 0 else '后'}"
            elif abs(diff) < 2592000:  # 30天
                days = int(abs(diff) / 86400)
                return f"{days}天{'前' if diff > 0 else '后'}"
            else:
                return "很久以前" if diff > 0 else "很久以后"
        except:
            return "无法计算"

    def convert_bytes(self):
        """转换字节单位"""
        value = self.byte_entry.text().strip()
        if not value:
            return

        try:
            # 获取数值和源单位
            numeric_value = float(value)
            source_unit = self.byte_unit_combo.currentText()
            source_power = self.units[source_unit]

            # 先转换为字节（基础单位）
            bytes_value = numeric_value * (1024 ** source_power)

            # 构建结果文本
            result_text = f"📊 字节单位转换结果\n\n"
            result_text += f"输入: {self.format_bytes_value(numeric_value)} {source_unit}\n\n"
            result_text += "转换结果:\n"
            result_text += "=" * 40 + "\n"

            # 转换为所有单位并格式化显示
            for unit, power in self.units.items():
                result = bytes_value / (1024 ** power)
                formatted_result = self.format_bytes_value(result)

                # 高亮显示源单位
                if unit == source_unit:
                    result_text += f"➤ {unit}: {formatted_result} ⭐\n"
                else:
                    result_text += f"  {unit}: {formatted_result}\n"

            # 添加额外信息
            result_text += "\n" + "=" * 40 + "\n"
            result_text += f"基础字节数: {self.format_bytes_value(bytes_value)} Bytes\n"

            # 更新右侧结果展示面板
            self.result_display.clear()
            self.result_display.setPlainText(result_text)

            self.statusBar().showMessage(f"✅ 已将 {self.format_bytes_value(numeric_value)} {source_unit} 转换为所有单位")

        except ValueError:
            self.statusBar().showMessage("❌ 请输入有效的数字")
        except Exception as e:
            self.statusBar().showMessage(f"❌ 转换错误: {str(e)}")

    def convert_timestamp(self):
        """转换时间戳为可读时间或反之"""
        value = self.timestamp_entry.text().strip()
        if not value:
            return

        try:
            # 获取转换类型
            conversion_type = self.timestamp_type_combo.currentText()

            if conversion_type == "时间戳转时间":
                # 时间戳转时间
                timestamp_value = float(value)

                # 转换为datetime对象
                dt = datetime.datetime.fromtimestamp(timestamp_value)

                # 主要结果 - 标准格式
                main_result = dt.strftime("%Y-%m-%d %H:%M:%S")

                # 详细信息 - 中文格式和星期
                weekdays = ["星期一", "星期二", "星期三", "星期四", "星期五", "星期六", "星期日"]
                weekday = weekdays[dt.weekday()]
                detail_result = f"{dt.year}年{dt.month:02d}月{dt.day:02d}日 {weekday} {dt.hour:02d}时{dt.minute:02d}分{dt.second:02d}秒"

                # ISO格式
                iso_result = dt.isoformat()

                # 相对时间
                relative_result = self.get_relative_time(timestamp_value)

                # 构建结果文本
                result_text = f"⏰ 时间戳转换结果\n\n"
                result_text += f"输入时间戳: {value}\n\n"
                result_text += "转换结果:\n"
                result_text += "=" * 40 + "\n"
                result_text += f"标准格式: {main_result}\n"
                result_text += f"中文格式: {detail_result}\n"
                result_text += f"ISO格式: {iso_result}\n"
                result_text += f"相对时间: {relative_result}\n"

                # 更新状态栏
                self.statusBar().showMessage(f"✅ 已将时间戳 {value} 转换为日期时间")

            else:
                # 时间转时间戳
                try:
                    # 尝试解析时间字符串
                    dt = datetime.datetime.strptime(value, "%Y-%m-%d %H:%M:%S")
                except ValueError:
                    try:
                        # 尝试其他常见格式
                        dt = datetime.datetime.strptime(value, "%Y/%m/%d %H:%M:%S")
                    except ValueError:
                        try:
                            dt = datetime.datetime.strptime(value, "%Y-%m-%d")
                        except ValueError:
                            raise ValueError("无法解析时间格式，请使用 YYYY-MM-DD HH:MM:SS 格式")

                # 获取时间戳
                timestamp = dt.timestamp()

                # 主要结果 - Unix时间戳
                main_result = f"Unix时间戳: {int(timestamp)} 秒"

                # 详细信息 - 毫秒时间戳
                detail_result = f"毫秒时间戳: {int(timestamp * 1000)} 毫秒"

                # ISO格式
                iso_result = dt.isoformat()

                # 相对时间
                relative_result = self.get_relative_time(timestamp)

                # 构建结果文本
                result_text = f"⏰ 时间转换结果\n\n"
                result_text += f"输入时间: {value}\n\n"
                result_text += "转换结果:\n"
                result_text += "=" * 40 + "\n"
                result_text += f"{main_result}\n"
                result_text += f"{detail_result}\n"
                result_text += f"ISO格式: {iso_result}\n"
                result_text += f"相对时间: {relative_result}\n"

                # 更新状态栏
                self.statusBar().showMessage(f"✅ 已将日期时间 {value} 转换为时间戳")

            # 更新右侧结果展示面板
            self.result_display.clear()
            self.result_display.setPlainText(result_text)

        except ValueError as e:
            if "无法解析时间格式" in str(e):
                self.statusBar().showMessage(f"❌ {str(e)}")
            else:
                self.statusBar().showMessage("❌ 请输入有效的时间戳或时间格式")
        except Exception as e:
            self.statusBar().showMessage(f"❌ 转换错误: {str(e)}")

    def update_timestamp_placeholder(self):
        """根据选择的转换类型更新占位符文本"""
        conversion_type = self.timestamp_type_combo.currentText()
        if conversion_type == "时间戳转时间":
            self.timestamp_entry.clear()
            self.timestamp_entry.setPlaceholderText("请输入Unix时间戳值，例如: 1609459200")
            self.statusBar().showMessage("🟢 请输入Unix时间戳值，例如: 1609459200")
        else:
            self.timestamp_entry.clear()
            self.timestamp_entry.setPlaceholderText("请输入时间，例如: 2021-01-01 00:00:00")
            self.statusBar().showMessage("🟢 请输入时间，例如: 2021-01-01 00:00:00")

    def set_current_time(self):
        """根据转换类型设置当前时间或时间戳"""
        # 获取转换类型
        conversion_type = self.timestamp_type_combo.currentText()

        if conversion_type == "时间转时间戳":
            # 如果是时间转时间戳，设置当前时间
            now = datetime.datetime.now()
            self.timestamp_entry.setText(now.strftime("%Y-%m-%d %H:%M:%S"))
        else:
            # 如果是时间戳转时间，设置当前时间戳
            current_timestamp = time.time()
            self.timestamp_entry.setText(str(int(current_timestamp)))

        # 转换
        self.convert_timestamp()

    def validate_currency_input(self):
        """验证货币输入是否为有效数字"""
        value = self.usd_entry.text().strip()
        if not value:
            return

        try:
            # 允许小数输入
            float(value)
            self.statusBar().showMessage("🟢 准备就绪")
        except ValueError:
            self.statusBar().showMessage("⚠️ 请输入有效的数字")

    def update_manual_rate(self):
        """更新手动设置的汇率"""
        try:
            rate = float(self.rate_entry.text())
            if rate <= 0:
                self.statusBar().showMessage("⚠️ 汇率必须大于0")
                return

            self.exchange_rate = rate
            self.statusBar().showMessage(f"✅ 已手动更新汇率: 1美元 = {rate}人民币")
            self.update_time_label.setText("🕒 手动设置")
            # 如果已有输入，自动转换
            if self.usd_entry.text().strip():
                self.convert_currency()
        except ValueError:
            self.statusBar().showMessage("⚠️ 请输入有效的汇率数字")
            
    def get_exchange_rate(self):
        """获取实时汇率数据（从中国银行官网获取）"""
        self.statusBar().showMessage("🔄 正在获取最新汇率...")

        # 检查是否安装了BeautifulSoup
        if not BS4_AVAILABLE:
            self.statusBar().showMessage("⚠️ 未安装BeautifulSoup库，无法从中国银行网站获取汇率。使用备用数据源...")
            self.get_exchange_rate_backup()
            return

        try:
            # 使用中国银行官网获取美元兑人民币汇率
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }

            # 中国银行外汇牌价网址
            url = "https://www.boc.cn/sourcedb/whpj/enindex_1619.html"

            response = requests.get(url, headers=headers, timeout=10)

            if response.status_code == 200:
                # 使用BeautifulSoup解析HTML
                soup = BeautifulSoup(response.text, 'html.parser')

                # 找到汇率表格
                table = soup.find('table', {'bgcolor': '#EAEAEA'})
                if table:
                    # 查找所有行
                    rows = table.find_all('tr')

                    # 遍历行寻找USD(美元)汇率
                    for row in rows[1:]:  # 跳过表头行
                        cells = row.find_all('td')
                        if len(cells) >= 7:  # 确保行有足够的单元格
                            currency_name = cells[0].text.strip()
                            if currency_name == 'USD':
                                # 获取卖出价作为汇率
                                selling_rate_text = cells[3].text.strip()
                                if selling_rate_text and selling_rate_text != '--':
                                    try:
                                        selling_rate = float(selling_rate_text)
                                        # 中行牌价是按照100外币单位显示的，需要除以100
                                        self.exchange_rate = selling_rate / 100
                                        self.rate_entry.setText(str(round(self.exchange_rate, 4)))
                                        self.last_rate_update = datetime.datetime.now()

                                        # 更新状态和时间显示
                                        self.statusBar().showMessage(f"✅ 汇率更新成功: 1美元 = {self.exchange_rate}人民币")
                                        self.update_time_label.setText(f"🕒 更新于: {self.last_rate_update.strftime('%Y-%m-%d %H:%M')}")

                                        # 如果已有输入，自动转换
                                        if self.usd_entry.text().strip():
                                            self.convert_currency()
                                        return
                                    except ValueError:
                                        self.statusBar().showMessage(f"❌ 解析汇率数据失败，数值格式错误: {selling_rate_text}")
                
                # 尝试使用中间价(Middle Rate)
                table = soup.find('table', {'bgcolor': '#EAEAEA'})
                if table:
                    for row in table.find_all('tr')[1:]:
                        cells = row.find_all('td')
                        if len(cells) >= 7:
                            currency_name = cells[0].text.strip()
                            if currency_name == 'USD':
                                middle_rate_text = cells[5].text.strip()
                                if middle_rate_text and middle_rate_text != '--':
                                    try:
                                        middle_rate = float(middle_rate_text)
                                        self.exchange_rate = middle_rate / 100
                                        self.rate_entry.setText(str(round(self.exchange_rate, 4)))
                                        self.last_rate_update = datetime.datetime.now()

                                        self.statusBar().showMessage(f"✅ 汇率更新成功(使用中间价): 1美元 = {self.exchange_rate}人民币")
                                        self.update_time_label.setText(f"🕒 更新于: {self.last_rate_update.strftime('%Y-%m-%d %H:%M')}")

                                        if self.usd_entry.text().strip():
                                            self.convert_currency()
                                        return
                                    except ValueError:
                                        pass

                # 如果未找到USD汇率或解析失败，尝试备用方法
                self.statusBar().showMessage("⚠️ 未在中国银行网站找到美元汇率，使用备用数据源...")
                self.get_exchange_rate_backup()
            else:
                # 如果请求失败，尝试备用方法
                self.statusBar().showMessage("⚠️ 无法连接到中国银行网站，使用备用数据源...")
                self.get_exchange_rate_backup()
        except Exception as e:
            self.statusBar().showMessage(f"❌ 获取汇率失败: {str(e)}")
            # 尝试备用方法
            self.get_exchange_rate_backup()
            
    def get_exchange_rate_backup(self):
        """备用方法获取汇率（使用开放API）"""
        try:
            # 首先尝试使用第一个备用API
            response = requests.get("https://open.er-api.com/v6/latest/USD", timeout=10)
            data = response.json()

            if response.status_code == 200 and "rates" in data and "CNY" in data["rates"]:
                self.exchange_rate = data["rates"]["CNY"]
                self.rate_entry.setText(str(round(self.exchange_rate, 4)))
                self.last_rate_update = datetime.datetime.now()

                # 更新状态和时间显示
                self.statusBar().showMessage(f"✅ 汇率更新成功(备用源1): 1美元 = {self.exchange_rate}人民币")
                self.update_time_label.setText(f"🕒 更新于: {self.last_rate_update.strftime('%Y-%m-%d %H:%M')}")

                # 如果已有输入，自动转换
                if self.usd_entry.text().strip():
                    self.convert_currency()
                return

            # 如果第一个备用API失败，尝试第二个备用API
            self.statusBar().showMessage("🔄 正在尝试第二个备用数据源...")
            response = requests.get("https://api.exchangerate-api.com/v4/latest/USD", timeout=10)
            data = response.json()

            if response.status_code == 200 and "rates" in data and "CNY" in data["rates"]:
                self.exchange_rate = data["rates"]["CNY"]
                self.rate_entry.setText(str(round(self.exchange_rate, 4)))
                self.last_rate_update = datetime.datetime.now()

                # 更新状态和时间显示
                self.statusBar().showMessage(f"✅ 汇率更新成功(备用源2): 1美元 = {self.exchange_rate}人民币")
                self.update_time_label.setText(f"🕒 更新于: {self.last_rate_update.strftime('%Y-%m-%d %H:%M')}")

                # 如果已有输入，自动转换
                if self.usd_entry.text().strip():
                    self.convert_currency()
                return

            # 所有API都失败，提示用户手动设置
            self.statusBar().showMessage("⚠️ 无法获取汇率数据，请手动设置汇率或稍后再试")
        except Exception as e:
            self.statusBar().showMessage(f"❌ 获取汇率失败: {str(e)}，请手动设置汇率")
            
    def convert_currency(self):
        """将美元转换为人民币"""
        value = self.usd_entry.text().strip()
        if not value:
            return

        try:
            # 获取美元金额
            usd_amount = float(value)

            # 转换为人民币
            cny_amount = usd_amount * self.exchange_rate

            # 格式化显示
            usd_formatted = f"${usd_amount:,.2f}"
            cny_formatted = f"¥{cny_amount:,.2f}"

            # 构建结果文本
            result_text = f"💰 货币汇率转换结果\n\n"
            result_text += f"输入金额: {usd_formatted} USD\n"
            result_text += f"当前汇率: {self.exchange_rate:.4f} CNY/USD\n\n"
            result_text += "转换结果:\n"
            result_text += "=" * 40 + "\n"
            result_text += f"💵 美元金额: {usd_formatted}\n"
            result_text += f"💴 人民币金额: {cny_formatted}\n\n"
            result_text += "计算详情:\n"
            result_text += "=" * 40 + "\n"
            result_text += f"计算公式: {usd_amount} × {self.exchange_rate:.4f} = {cny_amount:.2f}\n"
            result_text += f"汇率更新时间: {self.update_time_label.text()}\n"

            # 更新右侧结果展示面板
            self.result_display.clear()
            self.result_display.setPlainText(result_text)

            # 更新状态栏
            self.statusBar().showMessage(f"✅ 已将 {usd_formatted} 转换为 {cny_formatted}")

        except ValueError:
            self.statusBar().showMessage("❌ 请输入有效的数字")
        except Exception as e:
            self.statusBar().showMessage(f"❌ 转换错误: {str(e)}")

def main():
    """主函数"""
    app = QApplication(sys.argv)
    window = ConverterApp()
    window.show()
    sys.exit(app.exec())

if __name__ == "__main__":
    main()


