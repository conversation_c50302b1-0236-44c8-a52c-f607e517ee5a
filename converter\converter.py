"""
单位转换工具 - PySide6现代化版本
基于spliced_pictures_qt.py的设计风格，实现UI美化和功能迁移
支持字节单位转换、时间戳转换、货币汇率转换
"""

import sys
import time
import datetime
import requests  # 用于获取实时汇率
import json
import math
from pathlib import Path

from PySide6.QtCore import Qt, QTimer, QThread, Signal
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QPushButton,
    QLabel, QSplitter, QTabWidget, QTextEdit, QLineEdit, QComboBox,
    QMessageBox, QDialog, QProgressBar, QGraphicsDropShadowEffect
)
from PySide6.QtGui import QGuiApplication, QFont, QPainter, QPixmap, QColor, QShortcut, QKeySequence
from PySide6.QtCore import QPointF

# 尝试导入BeautifulSoup，如果不可用，记录错误
try:
    from bs4 import BeautifulSoup
    BS4_AVAILABLE = True
except ImportError:
    BS4_AVAILABLE = False

class ConfigManager:
    """配置管理器 - 参考spliced_pictures_qt.py的配置管理模式"""

    def __init__(self, config_file: str = "converter_config.json"):
        self.config_file = Path(__file__).parent / config_file
        self.config = self.load_config()

    def load_config(self) -> dict:
        """加载配置文件"""
        # 默认配置
        default_config = {
            "ui": {
                "window_width": 1000,
                "window_height": 800,
                "window_x": -1,  # -1表示居中
                "window_y": -1,  # -1表示居中
                "splitter_sizes": [400, 600]
            },
            "converter": {
                "default_byte_unit": "字节 (B)",
                "default_timestamp_type": "时间戳转时间",
                "exchange_rate": 7.2,
                "auto_convert": True,
                "remember_inputs": True
            },
            "network": {
                "timeout": 10,
                "retry_count": 3,
                "use_backup_api": True
            }
        }

        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    loaded_config = json.load(f)

                # 配置升级：确保所有新配置项都存在
                config_updated = False

                # 检查并添加缺失的配置项
                for section, section_data in default_config.items():
                    if section not in loaded_config:
                        loaded_config[section] = section_data
                        config_updated = True
                    elif isinstance(section_data, dict):
                        for key, value in section_data.items():
                            if key not in loaded_config[section]:
                                loaded_config[section][key] = value
                                config_updated = True

                # 如果配置有更新，保存到文件
                if config_updated:
                    self.save_config(loaded_config)

                return loaded_config
            else:
                # 创建新的配置文件
                self.save_config(default_config)
                return default_config
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            return default_config

    def save_config(self, config: dict = None) -> None:
        """保存配置文件"""
        try:
            config_to_save = config or self.config
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_to_save, f, ensure_ascii=False, indent=4)
        except Exception as e:
            print(f"保存配置文件失败: {e}")

    def get(self, key_path: str, default=None):
        """获取配置值，支持点分隔的路径"""
        keys = key_path.split('.')
        value = self.config
        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                return default
        return value

    def set(self, key_path: str, value) -> None:
        """设置配置值，支持点分隔的路径"""
        keys = key_path.split('.')
        config = self.config
        for key in keys[:-1]:
            if key not in config:
                config[key] = {}
            config = config[key]
        config[keys[-1]] = value
        self.save_config()


class QLoadingDialog(QDialog):
    """现代化加载对话框 - 参考spliced_pictures_qt.py的实现"""
    def __init__(self, parent=None, message="正在加载...", show_progress=False):
        super().__init__(parent)
        self.setWindowTitle("请稍候")
        self.setModal(True)
        self.setWindowFlags(Qt.Dialog | Qt.FramelessWindowHint)
        self.setAttribute(Qt.WA_TranslucentBackground)

        # 创建主布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # 创建内容容器
        self.container = QWidget(self)
        self.container.setObjectName("container")
        container_layout = QVBoxLayout(self.container)
        container_layout.setContentsMargins(40, 40, 40, 40)
        container_layout.setSpacing(25)

        # 添加加载动画
        self.spinner = QLabel()
        self.spinner.setFixedSize(64, 64)
        self.spinner.setObjectName("spinner")
        container_layout.addWidget(self.spinner, 0, Qt.AlignCenter)

        # 添加加载提示文本
        self.label = QLabel(message)
        self.label.setAlignment(Qt.AlignCenter)
        self.label.setObjectName("message")
        self.label.setWordWrap(True)
        container_layout.addWidget(self.label)

        # 可选的进度条
        self.progress_bar = None
        if show_progress:
            self.progress_bar = QProgressBar()
            self.progress_bar.setObjectName("progressBar")
            self.progress_bar.setRange(0, 100)
            container_layout.addWidget(self.progress_bar)

        layout.addWidget(self.container)
        self.setFixedSize(450, 250 if show_progress else 220)

        # 现代化样式
        self.setStyleSheet("""
            QDialog {
                background-color: rgba(0, 0, 0, 180);
            }
            QWidget#container {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff, stop:1 #f8f9fa);
                border-radius: 15px;
                border: 1px solid rgba(255, 255, 255, 0.8);
            }
            QLabel#message {
                font-family: "Microsoft YaHei", "Segoe UI";
                font-size: 15px;
                font-weight: 500;
                color: #2c3e50;
                padding: 10px;
                background: transparent;
            }
            QLabel#spinner {
                background: transparent;
            }
            QProgressBar#progressBar {
                border: none;
                border-radius: 8px;
                background-color: #e9ecef;
                height: 8px;
                text-align: center;
            }
            QProgressBar#progressBar::chunk {
                border-radius: 8px;
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #4CAF50, stop:1 #2196F3);
            }
        """)

        # 添加阴影效果
        shadow = QGraphicsDropShadowEffect(self)
        shadow.setBlurRadius(30)
        shadow.setColor(QColor(0, 0, 0, 80))
        shadow.setOffset(0, 5)
        self.container.setGraphicsEffect(shadow)

        self.start_animation()

    def start_animation(self):
        """启动现代化加载动画"""
        self.rotation = 0
        self.animation_timer = QTimer(self)
        self.animation_timer.timeout.connect(self.update_rotation)
        self.animation_timer.start(16)  # 60 FPS

    def update_rotation(self):
        """更新旋转角度"""
        self.rotation = (self.rotation + 6) % 360

        # 创建现代化的加载图标
        size = 64
        pixmap = QPixmap(size, size)
        pixmap.fill(Qt.transparent)

        try:
            painter = QPainter()
            if not painter.begin(pixmap):
                return

            painter.setRenderHint(QPainter.Antialiasing)

            center = QPointF(size/2, size/2)
            radius = size/2 - 8

            # 绘制多个圆点组成的加载动画
            for i in range(8):
                angle = i * 45 + self.rotation
                x = center.x() + radius * 0.7 * math.cos(math.radians(angle))
                y = center.y() + radius * 0.7 * math.sin(math.radians(angle))

                # 计算透明度（创建拖尾效果）
                alpha = max(0.2, 1.0 - (i * 0.12))

                # 设置颜色
                color = QColor("#1a73e8")
                color.setAlphaF(alpha)
                painter.setBrush(color)
                painter.setPen(Qt.NoPen)

                # 绘制圆点
                painter.drawEllipse(QPointF(x, y), 4, 4)

            painter.end()
            self.spinner.setPixmap(pixmap)

        except Exception as e:
            print(f"绘制加载动画失败: {e}")

    def update_message(self, message):
        """更新加载消息"""
        self.label.setText(message)

    def update_progress(self, value):
        """更新进度条"""
        if self.progress_bar:
            self.progress_bar.setValue(value)

    def closeEvent(self, event):
        """关闭事件 - 停止动画"""
        if hasattr(self, 'animation_timer'):
            self.animation_timer.stop()
        event.accept()


class ExchangeRateWorker(QThread):
    """汇率获取后台线程"""
    rate_updated = Signal(float, str)  # 汇率值, 数据源
    error_occurred = Signal(str)  # 错误信息

    def __init__(self):
        super().__init__()

    def run(self):
        """在后台线程中获取汇率"""
        try:
            # 首先尝试中国银行官网
            rate, source = self.get_rate_from_boc()
            if rate:
                self.rate_updated.emit(rate, source)
                return

            # 尝试备用API
            rate, source = self.get_rate_from_backup()
            if rate:
                self.rate_updated.emit(rate, source)
                return

            self.error_occurred.emit("所有汇率数据源都无法访问")

        except Exception as e:
            self.error_occurred.emit(f"获取汇率失败: {str(e)}")

    def get_rate_from_boc(self):
        """从中国银行获取汇率"""
        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            url = "https://www.boc.cn/sourcedb/whpj/enindex_1619.html"
            response = requests.get(url, headers=headers, timeout=10)

            if response.status_code == 200 and BS4_AVAILABLE:
                from bs4 import BeautifulSoup
                soup = BeautifulSoup(response.text, 'html.parser')

                # 查找汇率表格
                table = soup.find('table', {'bgcolor': '#EAEAEA'})
                if table:
                    rows = table.find_all('tr')
                    for row in rows[1:]:
                        cells = row.find_all('td')
                        if len(cells) >= 4:
                            currency = cells[0].get_text(strip=True)
                            if "USD" in currency or "美元" in currency:
                                selling_rate_text = cells[3].get_text(strip=True)
                                try:
                                    selling_rate = float(selling_rate_text)
                                    return selling_rate / 100, "中国银行官网"
                                except ValueError:
                                    continue
            return None, None
        except:
            return None, None

    def get_rate_from_backup(self):
        """从备用API获取汇率"""
        try:
            # 尝试第一个备用API
            response = requests.get("https://open.er-api.com/v6/latest/USD", timeout=10)
            if response.status_code == 200:
                data = response.json()
                if "rates" in data and "CNY" in data["rates"]:
                    return data["rates"]["CNY"], "备用API1"

            # 尝试第二个备用API
            response = requests.get("https://api.exchangerate-api.com/v4/latest/USD", timeout=10)
            if response.status_code == 200:
                data = response.json()
                if "rates" in data and "CNY" in data["rates"]:
                    return data["rates"]["CNY"], "备用API2"

            return None, None
        except:
            return None, None


# 复用spliced_pictures_qt.py的现代化样式表
STYLE_SHEET = """
QWidget {
    font-family: 'Microsoft YaHei', 'Segoe UI', 'SF Pro Display', Arial, sans-serif;
    font-size: 9pt;
    color: #2c3e50;
    selection-background-color: #e3f2fd;
}

QMainWindow {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #f8f9fa, stop:1 #e9ecef);
}

QPushButton {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #1a73e8, stop:1 #1557b0);
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 5px;
    font-weight: 500;
    min-width: 60px;
    min-height: 26px;
    font-size: 9pt;
}

QPushButton:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #1557b0, stop:1 #0d47a1);
}

QPushButton:pressed {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #0d47a1, stop:1 #1557b0);
}

QPushButton:disabled {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #e9ecef, stop:1 #dee2e6);
    color: #adb5bd;
    border: 1px solid #dee2e6;
}

QLineEdit {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #ffffff, stop:1 #f8f9fa);
    border: 2px solid #dee2e6;
    border-radius: 4px;
    padding: 4px 8px;
    min-height: 20px;
    font-size: 9pt;
    color: #495057;
}

QLineEdit:hover {
    border: 2px solid #2196f3;
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #f8f9fa, stop:1 #e9ecef);
}

QLineEdit:focus {
    border: 2px solid #1a73e8;
    background: white;
}

QComboBox {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 8px 12px;
    min-width: 80px;
    min-height: 24px;
    font-size: 10pt;
    color: #495057;
    font-weight: 500;
}

QComboBox:hover {
    border: 1px solid #2196f3;
    background: #f8f9fa;
}

QComboBox:focus {
    border: 2px solid #1a73e8;
    background: white;
}

QComboBox::drop-down {
    subcontrol-origin: padding;
    subcontrol-position: top right;
    width: 30px;
    border-left: 1px solid #dee2e6;
    border-top-right-radius: 8px;
    border-bottom-right-radius: 8px;
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #ffffff, stop:1 #f8f9fa);
}

QComboBox::drop-down:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #f8f9fa, stop:1 #e9ecef);
}

QComboBox::down-arrow {
    image: none;
    border: none;
    width: 0;
    height: 0;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 6px solid #6c757d;
    margin: 0px;
}

QComboBox::down-arrow:hover {
    border-top: 6px solid #2196f3;
}

QComboBox QAbstractItemView {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    background: white;
    selection-background-color: #e3f2fd;
    selection-color: #1a73e8;
    padding: 4px;
    font-size: 10pt;
}

QComboBox QAbstractItemView::item {
    height: 32px;
    padding: 4px 12px;
    border-radius: 4px;
    margin: 1px;
}

QComboBox QAbstractItemView::item:hover {
    background: #f0f8ff;
    color: #1a73e8;
}

QComboBox QAbstractItemView::item:selected {
    background: #e3f2fd;
    color: #1a73e8;
    font-weight: 600;
}

QTextEdit {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #ffffff, stop:1 #f8f9fa);
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 8px;
    font-size: 10pt;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
}

QTabWidget::pane {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #ffffff, stop:1 #f8f9fa);
}

QTabBar::tab {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #f8f9fa, stop:1 #e9ecef);
    border: 1px solid #dee2e6;
    padding: 8px 16px;
    margin-right: 2px;
    border-top-left-radius: 6px;
    border-top-right-radius: 6px;
}

QTabBar::tab:selected {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #1a73e8, stop:1 #1557b0);
    color: white;
    border-bottom: none;
}

QTabBar::tab:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #e3f2fd, stop:1 #bbdefb);
}

QSplitter::handle {
    background: #e9ecef;
    border: none;
    margin: 0px 8px;
}

QSplitter::handle:horizontal {
    width: 1px;
    margin: 0px 8px;
}

QSplitter::handle:hover {
    background: #2196f3;
    width: 2px;
}

QLabel {
    color: #495057;
}

QStatusBar {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #f8f9fa, stop:1 #e9ecef);
    border-top: 1px solid #dee2e6;
    padding: 4px;
}
"""


class ConverterApp(QMainWindow):
    """
    现代化单位转换工具 - PySide6版本
    支持:
    - 字节单位转换 (B, KB, MB, GB, TB, PB)
    - 时间戳转换
    - 货币汇率转换
    """

    def __init__(self):
        super().__init__()
        self.setWindowTitle("🔧 单位转换工具")

        # 初始化配置管理器
        self.config_manager = ConfigManager()

        # 设置窗口大小和居中显示
        self.setup_window()

        # Unit conversion factors (powers of 1024)
        self.units = {
            "字节 (B)": 0,
            "千字节 (KB)": 1,
            "兆字节 (MB)": 2,
            "千兆字节 (GB)": 3,
            "太字节 (TB)": 4,
            "拍字节 (PB)": 5
        }

        # 初始化汇率相关变量
        self.exchange_rate = self.config_manager.get("converter.exchange_rate", 7.2)
        self.last_rate_update = None  # 上次汇率更新时间

        # 创建主界面
        self.setup_ui()

        # 应用现代化样式
        self.setStyleSheet(STYLE_SHEET)

        # 加载用户设置
        self.load_settings()

        # 设置键盘快捷键
        self.setup_shortcuts()

        # 初始化加载对话框
        self.loading_dialog = None

    def setup_window(self):
        """设置窗口属性"""
        # 从配置获取窗口大小
        window_width = self.config_manager.get("ui.window_width", 1000)
        window_height = self.config_manager.get("ui.window_height", 800)
        window_x = self.config_manager.get("ui.window_x", -1)
        window_y = self.config_manager.get("ui.window_y", -1)

        # 获取屏幕几何信息
        screen = QGuiApplication.primaryScreen().geometry()

        # 计算位置
        if window_x == -1 or window_y == -1:
            # 居中显示
            x = (screen.width() - window_width) // 2
            y = (screen.height() - window_height) // 2
        else:
            # 使用保存的位置
            x = window_x
            y = window_y

        self.setGeometry(x, y, window_width, window_height)
        self.setMinimumSize(900, 700)




    def setup_ui(self):
        """设置现代化的用户界面 - 左右分栏布局"""
        # 创建中央组件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建主布局
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)

        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)

        # 左侧功能选择面板
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        left_layout.setContentsMargins(0, 0, 10, 0)  # 右边距10px，与分割线保持距离
        left_layout.setSpacing(0)  # 确保没有额外间距



        # 创建选项卡组件
        self.tab_widget = QTabWidget()
        left_layout.addWidget(self.tab_widget)

        # 添加三个选项卡
        self.byte_tab = QWidget()
        self.timestamp_tab = QWidget()
        self.currency_tab = QWidget()

        self.tab_widget.addTab(self.byte_tab, "📊 字节转换")
        self.tab_widget.addTab(self.timestamp_tab, "⏰ 时间戳")
        self.tab_widget.addTab(self.currency_tab, "💰 货币汇率")

        # 右侧结果展示面板
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        right_layout.setContentsMargins(10, 0, 0, 0)  # 左边距10px，与分割线保持距离，与左侧对称
        right_layout.setSpacing(0)  # 确保没有额外间距



        # 结果展示区域
        self.result_display = QTextEdit()
        self.result_display.setReadOnly(True)
        self.result_display.setPlaceholderText("转换结果将在此显示...\n\n💡 小贴士：\n• 使用 Ctrl+1/2/3 快速切换功能\n• 使用 F5 刷新汇率\n• 使用 Ctrl+R 清空所有输入\n• 右键点击结果可复制")

        # 添加右键菜单
        self.result_display.setContextMenuPolicy(Qt.CustomContextMenu)
        self.result_display.customContextMenuRequested.connect(self.show_result_context_menu)

        right_layout.addWidget(self.result_display)

        # 添加到分割器
        splitter.addWidget(left_widget)
        splitter.addWidget(right_widget)

        # 从配置获取分割器大小
        splitter_sizes = self.config_manager.get("ui.splitter_sizes", [400, 600])
        splitter.setSizes(splitter_sizes)

        # 保存分割器引用以便后续保存设置
        self.splitter = splitter

        # 设置各个转换功能的UI
        self.setup_byte_converter_ui()
        self.setup_timestamp_converter_ui()
        self.setup_currency_converter_ui()

        # 创建状态栏
        self.statusBar().showMessage("🟢 准备就绪")

    def load_settings(self):
        """加载用户设置"""
        try:
            # 加载字节转换默认单位
            default_byte_unit = self.config_manager.get("converter.default_byte_unit", "字节 (B)")
            if default_byte_unit in self.units:
                index = list(self.units.keys()).index(default_byte_unit)
                self.byte_unit_combo.setCurrentIndex(index)

            # 加载时间戳转换默认类型
            default_timestamp_type = self.config_manager.get("converter.default_timestamp_type", "时间戳转时间")
            if default_timestamp_type in ["时间戳转时间", "时间转时间戳"]:
                index = 0 if default_timestamp_type == "时间戳转时间" else 1
                self.timestamp_type_combo.setCurrentIndex(index)

            # 加载汇率设置
            self.rate_entry.setText(str(self.exchange_rate))

        except Exception as e:
            print(f"加载设置失败: {e}")

    def save_settings(self):
        """保存用户设置"""
        try:
            # 保存窗口大小和位置
            geometry = self.geometry()
            self.config_manager.set("ui.window_width", geometry.width())
            self.config_manager.set("ui.window_height", geometry.height())
            self.config_manager.set("ui.window_x", geometry.x())
            self.config_manager.set("ui.window_y", geometry.y())

            # 保存分割器大小
            if hasattr(self, 'splitter'):
                self.config_manager.set("ui.splitter_sizes", self.splitter.sizes())

            # 保存转换器设置
            self.config_manager.set("converter.default_byte_unit", self.byte_unit_combo.currentText())
            self.config_manager.set("converter.default_timestamp_type", self.timestamp_type_combo.currentText())
            self.config_manager.set("converter.exchange_rate", self.exchange_rate)

        except Exception as e:
            print(f"保存设置失败: {e}")

    def closeEvent(self, event):
        """窗口关闭事件 - 保存设置和清理资源"""
        try:
            # 停止汇率获取线程
            if hasattr(self, 'rate_worker') and self.rate_worker.isRunning():
                self.rate_worker.terminate()
                self.rate_worker.wait(1000)  # 等待1秒

            # 关闭加载对话框
            self.close_loading_dialog()

            # 保存设置
            self.save_settings()

            self.update_status("👋 感谢使用单位转换工具")

        except Exception as e:
            print(f"关闭窗口时发生错误: {e}")

        event.accept()

    def show_message(self, title: str, message: str, message_type: str = "info"):
        """统一的消息提示机制"""
        if message_type == "info":
            QMessageBox.information(self, title, message)
        elif message_type == "warning":
            QMessageBox.warning(self, title, message)
        elif message_type == "error":
            QMessageBox.critical(self, title, message)
        elif message_type == "question":
            return QMessageBox.question(self, title, message)

    def update_status(self, message: str, timeout: int = 0):
        """统一的状态栏更新机制"""
        self.statusBar().showMessage(message, timeout)

    def setup_byte_tooltips(self):
        """设置字节转换的工具提示"""
        self.byte_entry.setToolTip("输入要转换的数值，支持小数")
        self.byte_unit_combo.setToolTip("选择输入数值的单位")
        self.byte_convert_btn.setToolTip("开始转换，或按回车键快速转换")

    def setup_timestamp_tooltips(self):
        """设置时间戳转换的工具提示"""
        self.timestamp_entry.setToolTip("输入时间戳或时间字符串")
        self.timestamp_type_combo.setToolTip("选择转换类型：时间戳转时间 或 时间转时间戳")
        self.now_button.setToolTip("快速设置当前时间或时间戳")
        self.timestamp_convert_btn.setToolTip("开始转换，或按回车键快速转换")

    def setup_currency_tooltips(self):
        """设置货币转换的工具提示"""
        self.usd_entry.setToolTip("输入美元金额，支持小数")
        self.rate_entry.setToolTip("当前汇率，可手动修改或点击更新按钮获取最新汇率")
        self.update_rate_btn.setToolTip("从网络获取最新的美元兑人民币汇率")
        self.currency_convert_btn.setToolTip("开始转换，或按回车键快速转换")

    def setup_shortcuts(self):
        """设置键盘快捷键"""
        # Ctrl+1: 切换到字节转换
        shortcut_byte = QShortcut(QKeySequence("Ctrl+1"), self)
        shortcut_byte.activated.connect(lambda: self.tab_widget.setCurrentIndex(0))

        # Ctrl+2: 切换到时间戳转换
        shortcut_timestamp = QShortcut(QKeySequence("Ctrl+2"), self)
        shortcut_timestamp.activated.connect(lambda: self.tab_widget.setCurrentIndex(1))

        # Ctrl+3: 切换到货币转换
        shortcut_currency = QShortcut(QKeySequence("Ctrl+3"), self)
        shortcut_currency.activated.connect(lambda: self.tab_widget.setCurrentIndex(2))

        # F5: 刷新汇率
        shortcut_refresh = QShortcut(QKeySequence("F5"), self)
        shortcut_refresh.activated.connect(self.get_exchange_rate_with_loading)

        # Ctrl+R: 清空所有输入
        shortcut_clear = QShortcut(QKeySequence("Ctrl+R"), self)
        shortcut_clear.activated.connect(self.clear_all_inputs)

        # Esc: 关闭加载对话框
        shortcut_esc = QShortcut(QKeySequence("Escape"), self)
        shortcut_esc.activated.connect(self.close_loading_dialog)

    def clear_all_inputs(self):
        """清空所有输入框"""
        self.byte_entry.clear()
        self.timestamp_entry.clear()
        self.usd_entry.clear()
        self.result_display.clear()
        self.update_status("🔄 已清空所有输入")

    def show_loading(self, message="正在处理..."):
        """显示加载对话框"""
        if self.loading_dialog is None:
            self.loading_dialog = QLoadingDialog(self, message)
        else:
            self.loading_dialog.update_message(message)

        self.loading_dialog.show()
        QApplication.processEvents()

    def close_loading_dialog(self):
        """关闭加载对话框"""
        if self.loading_dialog:
            self.loading_dialog.close()
            self.loading_dialog = None

    def get_exchange_rate_with_loading(self):
        """带加载动画的汇率获取"""
        # 显示加载对话框
        self.show_loading("🔄 正在获取最新汇率...")

        # 禁用更新按钮
        self.update_rate_btn.setEnabled(False)
        self.update_rate_btn.setText("🔄 获取中...")

        # 创建并启动后台线程
        self.rate_worker = ExchangeRateWorker()
        self.rate_worker.rate_updated.connect(self.on_rate_updated)
        self.rate_worker.error_occurred.connect(self.on_rate_error)
        self.rate_worker.finished.connect(self.on_rate_finished)
        self.rate_worker.start()

    def on_rate_updated(self, rate, source):
        """汇率更新成功"""
        self.exchange_rate = rate
        self.rate_entry.setText(str(round(rate, 4)))
        self.last_rate_update = datetime.datetime.now()

        self.update_status(f"✅ 汇率更新成功({source}): 1美元 = {rate:.4f}人民币")
        self.update_time_label.setText(f"🕒 更新于: {self.last_rate_update.strftime('%Y-%m-%d %H:%M')}")

        # 如果已有输入，自动转换
        if self.usd_entry.text().strip():
            self.convert_currency()

    def on_rate_error(self, error_message):
        """汇率获取失败"""
        self.update_status(f"❌ {error_message}")
        self.show_message("汇率获取失败", error_message, "warning")

    def on_rate_finished(self):
        """汇率获取完成（无论成功失败）"""
        # 关闭加载对话框
        self.close_loading_dialog()

        # 恢复按钮状态
        self.update_rate_btn.setEnabled(True)
        self.update_rate_btn.setText("🔄 更新汇率")

    def show_result_context_menu(self, position):
        """显示结果区域的右键菜单"""
        from PySide6.QtWidgets import QMenu

        menu = QMenu(self)

        # 复制全部结果
        copy_all_action = menu.addAction("📋 复制全部结果")
        copy_all_action.triggered.connect(self.copy_all_results)

        # 复制选中文本
        if self.result_display.textCursor().hasSelection():
            copy_selected_action = menu.addAction("📄 复制选中文本")
            copy_selected_action.triggered.connect(self.copy_selected_text)

        menu.addSeparator()

        # 清空结果
        clear_action = menu.addAction("🗑️ 清空结果")
        clear_action.triggered.connect(self.result_display.clear)

        # 保存结果到文件
        save_action = menu.addAction("💾 保存结果到文件")
        save_action.triggered.connect(self.save_results_to_file)

        menu.exec_(self.result_display.mapToGlobal(position))

    def copy_all_results(self):
        """复制全部结果到剪贴板"""
        text = self.result_display.toPlainText()
        if text:
            QApplication.clipboard().setText(text)
            self.update_status("✅ 已复制全部结果到剪贴板", 2000)

    def copy_selected_text(self):
        """复制选中文本到剪贴板"""
        cursor = self.result_display.textCursor()
        if cursor.hasSelection():
            QApplication.clipboard().setText(cursor.selectedText())
            self.update_status("✅ 已复制选中文本到剪贴板", 2000)

    def save_results_to_file(self):
        """保存结果到文件"""
        from PySide6.QtWidgets import QFileDialog

        text = self.result_display.toPlainText()
        if not text:
            self.show_message("提示", "没有结果可保存", "info")
            return

        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存转换结果",
            f"转换结果_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.txt",
            "文本文件 (*.txt);;所有文件 (*)"
        )

        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(text)
                self.update_status(f"✅ 结果已保存到: {file_path}", 3000)
            except Exception as e:
                self.show_message("保存失败", f"无法保存文件: {str(e)}", "error")

    def setup_byte_converter_ui(self):
        """设置字节转换UI - 使用PySide6组件"""
        layout = QVBoxLayout(self.byte_tab)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)

        # 输入区域标题
        title_label = QLabel("📊 字节单位转换")
        title_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #2c3e50; margin-bottom: 10px;")
        layout.addWidget(title_label)

        # 数值输入区域
        value_label = QLabel("输入数值:")
        layout.addWidget(value_label)

        self.byte_entry = QLineEdit()
        self.byte_entry.setPlaceholderText("请输入数值...")
        layout.addWidget(self.byte_entry)

        # 单位选择区域
        unit_label = QLabel("选择单位:")
        layout.addWidget(unit_label)

        self.byte_unit_combo = QComboBox()
        self.byte_unit_combo.addItems(list(self.units.keys()))
        self.byte_unit_combo.setCurrentIndex(0)
        layout.addWidget(self.byte_unit_combo)

        # 转换按钮
        self.byte_convert_btn = QPushButton("🔄 开始转换")
        self.byte_convert_btn.clicked.connect(self.convert_bytes)
        self.byte_convert_btn.setEnabled(False)  # 初始禁用
        layout.addWidget(self.byte_convert_btn)

        # 添加弹性空间
        layout.addStretch()

        # 绑定事件
        self.byte_entry.returnPressed.connect(self.convert_bytes)
        self.byte_entry.textChanged.connect(self.validate_byte_input)
        self.byte_unit_combo.currentTextChanged.connect(lambda: self.convert_bytes())

        # 添加工具提示
        self.setup_byte_tooltips()

    def setup_timestamp_converter_ui(self):
        """设置时间戳转换UI - 使用PySide6组件"""
        layout = QVBoxLayout(self.timestamp_tab)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)

        # 输入区域标题
        title_label = QLabel("⏰ 时间戳转换")
        title_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #2c3e50; margin-bottom: 10px;")
        layout.addWidget(title_label)

        # 时间输入区域
        input_label = QLabel("输入时间戳/时间:")
        layout.addWidget(input_label)

        self.timestamp_entry = QLineEdit()
        self.timestamp_entry.setPlaceholderText("请输入时间戳或时间...")
        layout.addWidget(self.timestamp_entry)

        # 转换类型选择
        type_label = QLabel("转换类型:")
        layout.addWidget(type_label)

        self.timestamp_type_combo = QComboBox()
        self.timestamp_type_combo.addItems(["时间戳转时间", "时间转时间戳"])
        self.timestamp_type_combo.setCurrentIndex(0)
        layout.addWidget(self.timestamp_type_combo)

        # 按钮区域
        button_layout = QHBoxLayout()

        # 当前时间按钮
        self.now_button = QPushButton("📅 当前时间")
        self.now_button.clicked.connect(self.set_current_time)
        button_layout.addWidget(self.now_button)

        # 转换按钮
        self.timestamp_convert_btn = QPushButton("🔄 开始转换")
        self.timestamp_convert_btn.clicked.connect(self.convert_timestamp)
        self.timestamp_convert_btn.setEnabled(False)  # 初始禁用
        button_layout.addWidget(self.timestamp_convert_btn)

        layout.addLayout(button_layout)

        # 添加弹性空间
        layout.addStretch()

        # 绑定事件
        self.timestamp_entry.returnPressed.connect(self.convert_timestamp)
        self.timestamp_entry.textChanged.connect(self.validate_timestamp_input)
        self.timestamp_type_combo.currentTextChanged.connect(self.update_timestamp_placeholder)
        self.update_timestamp_placeholder()

        # 添加工具提示
        self.setup_timestamp_tooltips()

    def validate_byte_input(self):
        """Validate that the input is a valid number for byte conversion"""
        value = self.byte_value_var.get().strip()
        if not value:
            return

        try:
            # Allow for decimal numbers
            float(value)
            self.status_var.set("准备就绪")
        except ValueError:
            self.status_var.set("请输入有效的数字")

    def validate_timestamp_input(self):
        """Validate that the input is a valid timestamp"""
        value = self.timestamp_value_var.get().strip()
        if not value:
            return

        try:
            # Allow for integer or decimal numbers
            float(value)
            self.status_var.set("准备就绪")
        except ValueError:
            self.status_var.set("请输入有效的时间戳")

    def format_bytes_value(self, value):
        """Format byte values for better display"""
        if value == 0:
            return "0"

        # 对于非常大的数字使用科学计数法
        if abs(value) >= 1e15:
            return f"{value:.2e}"

        # 对于非常小的数字使用科学计数法
        if abs(value) < 1e-6 and value != 0:
            return f"{value:.2e}"

        # 对于整数，直接显示
        if value == int(value):
            return f"{int(value):,}"

        # 对于小数，根据大小确定精度
        if abs(value) >= 1000:
            return f"{value:,.2f}"
        elif abs(value) >= 1:
            return f"{value:.4f}"
        else:
            return f"{value:.6f}".rstrip('0').rstrip('.')

    def get_relative_time(self, timestamp):
        """Get relative time description"""
        try:
            now = time.time()
            diff = now - timestamp

            if abs(diff) < 60:
                return "刚刚"
            elif abs(diff) < 3600:
                minutes = int(abs(diff) / 60)
                return f"{minutes}分钟{'前' if diff > 0 else '后'}"
            elif abs(diff) < 86400:
                hours = int(abs(diff) / 3600)
                return f"{hours}小时{'前' if diff > 0 else '后'}"
            elif abs(diff) < 2592000:  # 30 days
                days = int(abs(diff) / 86400)
                return f"{days}天{'前' if diff > 0 else '后'}"
            elif abs(diff) < 31536000:  # 365 days
                months = int(abs(diff) / 2592000)
                return f"{months}个月{'前' if diff > 0 else '后'}"
            else:
                years = int(abs(diff) / 31536000)
                return f"{years}年{'前' if diff > 0 else '后'}"
        except:
            return "无法计算"

    def convert_bytes(self):
        """Convert the input value to all byte units"""
        value = self.byte_value_var.get().strip()
        if not value:
            return

        try:
            # Get the numeric value and source unit
            numeric_value = float(value)
            source_unit = self.byte_unit_var.get()
            source_power = self.units[source_unit]

            # Convert to bytes first (base unit)
            bytes_value = numeric_value * (1024 ** source_power)

            # 构建结果文本
            result_text = f"📊 字节单位转换结果\n\n"
            result_text += f"输入: {self.format_bytes_value(numeric_value)} {source_unit}\n\n"
            result_text += "转换结果:\n"
            result_text += "=" * 40 + "\n"

            # Convert to all units and format for display
            for unit, power in self.units.items():
                result = bytes_value / (1024 ** power)
                formatted_result = self.format_bytes_value(result)

                # 高亮显示源单位
                if unit == source_unit:
                    result_text += f"➤ {unit}: {formatted_result} ⭐\n"
                else:
                    result_text += f"  {unit}: {formatted_result}\n"

            # 添加额外信息
            result_text += "\n" + "=" * 40 + "\n"
            result_text += f"基础字节数: {self.format_bytes_value(bytes_value)} Bytes\n"

            # 更新右侧结果展示面板
            self.result_display.delete("1.0", "end")
            self.result_display.insert("1.0", result_text)

            self.status_var.set(f"✅ 已将 {self.format_bytes_value(numeric_value)} {source_unit} 转换为所有单位")

        except ValueError:
            self.status_var.set("❌ 请输入有效的数字")
        except Exception as e:
            self.status_var.set(f"❌ 转换错误: {str(e)}")

    def convert_timestamp(self):
        """Convert the timestamp to human-readable date and time or vice versa"""
        value = self.timestamp_value_var.get().strip()
        if not value:
            return

        try:
            # 获取转换类型
            conversion_type = self.timestamp_type_var.get()

            if conversion_type == "时间戳转时间":
                # 时间戳转时间
                timestamp_value = float(value)

                # 转换为datetime对象
                dt = datetime.datetime.fromtimestamp(timestamp_value)

                # 主要结果 - 标准格式
                main_result = dt.strftime("%Y-%m-%d %H:%M:%S")

                # 详细信息 - 中文格式和星期
                weekdays = ["星期一", "星期二", "星期三", "星期四", "星期五", "星期六", "星期日"]
                weekday = weekdays[dt.weekday()]
                detail_result = f"{dt.year}年{dt.month:02d}月{dt.day:02d}日 {weekday} {dt.hour:02d}时{dt.minute:02d}分{dt.second:02d}秒"

                # ISO格式
                iso_result = dt.isoformat()

                # 相对时间
                relative_result = self.get_relative_time(timestamp_value)

                # 更新状态栏
                self.status_var.set(f"已将时间戳 {value} 转换为日期时间")

            else:
                # 时间转时间戳
                try:
                    # 尝试解析时间字符串
                    dt = datetime.datetime.strptime(value, "%Y-%m-%d %H:%M:%S")
                except ValueError:
                    try:
                        # 尝试其他常见格式
                        dt = datetime.datetime.strptime(value, "%Y/%m/%d %H:%M:%S")
                    except ValueError:
                        try:
                            dt = datetime.datetime.strptime(value, "%Y-%m-%d")
                        except ValueError:
                            raise ValueError("无法解析时间格式，请使用 YYYY-MM-DD HH:MM:SS 格式")

                # 获取时间戳
                timestamp = dt.timestamp()

                # 主要结果 - Unix时间戳
                main_result = f"Unix时间戳: {int(timestamp)} 秒"

                # 详细信息 - 毫秒时间戳
                detail_result = f"毫秒时间戳: {int(timestamp * 1000)} 毫秒"

                # ISO格式
                iso_result = dt.isoformat()

                # 相对时间
                relative_result = self.get_relative_time(timestamp)

                # 更新状态栏
                self.status_var.set(f"已将日期时间 {value} 转换为时间戳")

            # 构建结果文本
            result_text = f"⏰ 时间戳转换结果\n\n"
            result_text += f"输入: {value}\n"
            result_text += f"转换类型: {conversion_type}\n\n"
            result_text += "转换结果:\n"
            result_text += "=" * 40 + "\n"
            result_text += f"📅 主要结果:\n{main_result}\n\n"
            result_text += f"📝 详细信息:\n{detail_result}\n\n"
            result_text += f"🌐 ISO格式:\n{iso_result}\n\n"
            result_text += f"⏱️ 相对时间:\n{relative_result}\n"

            # 更新右侧结果展示面板
            self.result_display.delete("1.0", "end")
            self.result_display.insert("1.0", result_text)

            # 更新结果变量（保持兼容性）
            self.timestamp_results['main'].set(main_result)
            self.timestamp_results['detail'].set(detail_result)
            self.timestamp_results['iso'].set(iso_result)
            self.timestamp_results['relative'].set(relative_result)

        except ValueError as e:
            if "无法解析时间格式" in str(e):
                self.status_var.set(f"❌ {str(e)}")
            else:
                self.status_var.set("❌ 请输入有效的时间戳或时间格式")
        except Exception as e:
            self.statusBar().showMessage(f"❌ 转换错误: {str(e)}")

    def set_current_time(self):
        """Set current time or timestamp based on the conversion type"""
        # 获取转换类型
        conversion_type = self.timestamp_type_var.get()

        if conversion_type == "时间转时间戳":
            # 如果是时间转时间戳，设置当前时间
            now = datetime.datetime.now()
            self.timestamp_value_var.set(now.strftime("%Y-%m-%d %H:%M:%S"))
        else:
            # 如果是时间戳转时间，设置当前时间戳
            current_timestamp = time.time()
            self.timestamp_value_var.set(str(int(current_timestamp)))

        # 转换
        self.convert_timestamp()

    def setup_currency_converter_ui(self):
        """设置货币转换UI - 使用PySide6组件"""
        layout = QVBoxLayout(self.currency_tab)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(10)

        # 输入区域标题
        title_label = QLabel("💰 货币汇率转换")
        title_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #2c3e50; margin-bottom: 10px;")
        layout.addWidget(title_label)

        # 美元金额输入区域
        usd_label = QLabel("输入美元金额:")
        layout.addWidget(usd_label)

        self.usd_entry = QLineEdit()
        self.usd_entry.setPlaceholderText("请输入美元金额...")
        layout.addWidget(self.usd_entry)

        # 汇率设置区域
        rate_label = QLabel("当前汇率 (人民币/美元):")
        layout.addWidget(rate_label)

        self.rate_entry = QLineEdit()
        self.rate_entry.setText(str(self.exchange_rate))
        layout.addWidget(self.rate_entry)

        # 更新时间显示
        self.update_time_label = QLabel("🕒 尚未更新汇率")
        self.update_time_label.setStyleSheet("color: gray; font-size: 9pt;")
        layout.addWidget(self.update_time_label)

        # 按钮区域
        button_layout = QHBoxLayout()

        # 更新汇率按钮
        self.update_rate_btn = QPushButton("🔄 更新汇率")
        self.update_rate_btn.clicked.connect(self.get_exchange_rate_with_loading)
        button_layout.addWidget(self.update_rate_btn)

        # 转换按钮
        self.currency_convert_btn = QPushButton("💱 开始转换")
        self.currency_convert_btn.clicked.connect(self.convert_currency)
        self.currency_convert_btn.setEnabled(False)  # 初始禁用
        button_layout.addWidget(self.currency_convert_btn)

        layout.addLayout(button_layout)

        # 添加弹性空间
        layout.addStretch()

        # 绑定事件
        self.usd_entry.returnPressed.connect(self.convert_currency)
        self.usd_entry.textChanged.connect(self.validate_currency_input)
        self.rate_entry.textChanged.connect(self.update_manual_rate)

        # 添加工具提示
        self.setup_currency_tooltips()

    def validate_byte_input(self):
        """验证字节输入是否为有效数字"""
        value = self.byte_entry.text().strip()

        if not value:
            self.byte_convert_btn.setEnabled(False)
            self.byte_entry.setStyleSheet("")
            return

        try:
            # 允许小数输入
            float(value)
            self.update_status("🟢 准备就绪")
            self.byte_convert_btn.setEnabled(True)
            self.byte_entry.setStyleSheet("border: 2px solid #4CAF50;")
        except ValueError:
            self.update_status("⚠️ 请输入有效的数字")
            self.byte_convert_btn.setEnabled(False)
            self.byte_entry.setStyleSheet("border: 2px solid #f44336;")

    def validate_timestamp_input(self):
        """验证时间戳输入是否有效"""
        value = self.timestamp_entry.text().strip()

        if not value:
            self.timestamp_convert_btn.setEnabled(False)
            self.timestamp_entry.setStyleSheet("")
            return

        # 根据转换类型验证
        conversion_type = self.timestamp_type_combo.currentText()
        is_valid = False

        if conversion_type == "时间戳转时间":
            try:
                float(value)
                is_valid = True
            except ValueError:
                pass
        else:
            # 时间转时间戳，验证时间格式
            try:
                datetime.datetime.strptime(value, "%Y-%m-%d %H:%M:%S")
                is_valid = True
            except ValueError:
                try:
                    datetime.datetime.strptime(value, "%Y/%m/%d %H:%M:%S")
                    is_valid = True
                except ValueError:
                    try:
                        datetime.datetime.strptime(value, "%Y-%m-%d")
                        is_valid = True
                    except ValueError:
                        pass

        if is_valid:
            self.update_status("🟢 准备就绪")
            self.timestamp_convert_btn.setEnabled(True)
            self.timestamp_entry.setStyleSheet("border: 2px solid #4CAF50;")
        else:
            self.update_status("⚠️ 请输入有效的时间戳或时间格式")
            self.timestamp_convert_btn.setEnabled(False)
            self.timestamp_entry.setStyleSheet("border: 2px solid #f44336;")

    def format_bytes_value(self, value):
        """格式化字节值以便更好地显示"""
        if value == 0:
            return "0"

        # 对于非常大的数字使用科学计数法
        if abs(value) >= 1e15:
            return f"{value:.2e}"

        # 对于非常小的数字使用科学计数法
        if abs(value) < 1e-6 and value != 0:
            return f"{value:.2e}"

        # 对于整数，直接显示
        if value == int(value):
            return f"{int(value):,}"

        # 对于小数，根据大小确定精度
        if abs(value) >= 1000:
            return f"{value:,.2f}"
        elif abs(value) >= 1:
            return f"{value:.4f}"
        else:
            return f"{value:.6f}".rstrip('0').rstrip('.')

    def get_relative_time(self, timestamp):
        """获取相对时间描述"""
        try:
            now = time.time()
            diff = now - timestamp

            if abs(diff) < 60:
                return "刚刚"
            elif abs(diff) < 3600:
                minutes = int(abs(diff) / 60)
                return f"{minutes}分钟{'前' if diff > 0 else '后'}"
            elif abs(diff) < 86400:
                hours = int(abs(diff) / 3600)
                return f"{hours}小时{'前' if diff > 0 else '后'}"
            elif abs(diff) < 2592000:  # 30天
                days = int(abs(diff) / 86400)
                return f"{days}天{'前' if diff > 0 else '后'}"
            else:
                return "很久以前" if diff > 0 else "很久以后"
        except:
            return "无法计算"

    def convert_bytes(self):
        """转换字节单位"""
        value = self.byte_entry.text().strip()
        if not value:
            return

        try:
            # 获取数值和源单位
            numeric_value = float(value)
            source_unit = self.byte_unit_combo.currentText()
            source_power = self.units[source_unit]

            # 先转换为字节（基础单位）
            bytes_value = numeric_value * (1024 ** source_power)

            # 构建结果文本
            result_text = f"📊 字节单位转换结果\n\n"
            result_text += f"输入: {self.format_bytes_value(numeric_value)} {source_unit}\n\n"
            result_text += "转换结果:\n"
            result_text += "=" * 40 + "\n"

            # 转换为所有单位并格式化显示
            for unit, power in self.units.items():
                result = bytes_value / (1024 ** power)
                formatted_result = self.format_bytes_value(result)

                # 高亮显示源单位
                if unit == source_unit:
                    result_text += f"➤ {unit}: {formatted_result} ⭐\n"
                else:
                    result_text += f"  {unit}: {formatted_result}\n"

            # 添加额外信息
            result_text += "\n" + "=" * 40 + "\n"
            result_text += f"基础字节数: {self.format_bytes_value(bytes_value)} Bytes\n"

            # 更新右侧结果展示面板
            self.result_display.clear()
            self.result_display.setPlainText(result_text)

            self.statusBar().showMessage(f"✅ 已将 {self.format_bytes_value(numeric_value)} {source_unit} 转换为所有单位")

        except ValueError:
            self.statusBar().showMessage("❌ 请输入有效的数字")
        except Exception as e:
            self.statusBar().showMessage(f"❌ 转换错误: {str(e)}")

    def convert_timestamp(self):
        """转换时间戳为可读时间或反之"""
        value = self.timestamp_entry.text().strip()
        if not value:
            return

        try:
            # 获取转换类型
            conversion_type = self.timestamp_type_combo.currentText()

            if conversion_type == "时间戳转时间":
                # 时间戳转时间
                timestamp_value = float(value)

                # 转换为datetime对象
                dt = datetime.datetime.fromtimestamp(timestamp_value)

                # 主要结果 - 标准格式
                main_result = dt.strftime("%Y-%m-%d %H:%M:%S")

                # 详细信息 - 中文格式和星期
                weekdays = ["星期一", "星期二", "星期三", "星期四", "星期五", "星期六", "星期日"]
                weekday = weekdays[dt.weekday()]
                detail_result = f"{dt.year}年{dt.month:02d}月{dt.day:02d}日 {weekday} {dt.hour:02d}时{dt.minute:02d}分{dt.second:02d}秒"

                # ISO格式
                iso_result = dt.isoformat()

                # 相对时间
                relative_result = self.get_relative_time(timestamp_value)

                # 构建结果文本
                result_text = f"⏰ 时间戳转换结果\n\n"
                result_text += f"输入时间戳: {value}\n\n"
                result_text += "转换结果:\n"
                result_text += "=" * 40 + "\n"
                result_text += f"标准格式: {main_result}\n"
                result_text += f"中文格式: {detail_result}\n"
                result_text += f"ISO格式: {iso_result}\n"
                result_text += f"相对时间: {relative_result}\n"

                # 更新状态栏
                self.statusBar().showMessage(f"✅ 已将时间戳 {value} 转换为日期时间")

            else:
                # 时间转时间戳
                try:
                    # 尝试解析时间字符串
                    dt = datetime.datetime.strptime(value, "%Y-%m-%d %H:%M:%S")
                except ValueError:
                    try:
                        # 尝试其他常见格式
                        dt = datetime.datetime.strptime(value, "%Y/%m/%d %H:%M:%S")
                    except ValueError:
                        try:
                            dt = datetime.datetime.strptime(value, "%Y-%m-%d")
                        except ValueError:
                            raise ValueError("无法解析时间格式，请使用 YYYY-MM-DD HH:MM:SS 格式")

                # 获取时间戳
                timestamp = dt.timestamp()

                # 主要结果 - Unix时间戳
                main_result = f"Unix时间戳: {int(timestamp)} 秒"

                # 详细信息 - 毫秒时间戳
                detail_result = f"毫秒时间戳: {int(timestamp * 1000)} 毫秒"

                # ISO格式
                iso_result = dt.isoformat()

                # 相对时间
                relative_result = self.get_relative_time(timestamp)

                # 构建结果文本
                result_text = f"⏰ 时间转换结果\n\n"
                result_text += f"输入时间: {value}\n\n"
                result_text += "转换结果:\n"
                result_text += "=" * 40 + "\n"
                result_text += f"{main_result}\n"
                result_text += f"{detail_result}\n"
                result_text += f"ISO格式: {iso_result}\n"
                result_text += f"相对时间: {relative_result}\n"

                # 更新状态栏
                self.statusBar().showMessage(f"✅ 已将日期时间 {value} 转换为时间戳")

            # 更新右侧结果展示面板
            self.result_display.clear()
            self.result_display.setPlainText(result_text)

        except ValueError as e:
            if "无法解析时间格式" in str(e):
                self.statusBar().showMessage(f"❌ {str(e)}")
            else:
                self.statusBar().showMessage("❌ 请输入有效的时间戳或时间格式")
        except Exception as e:
            self.statusBar().showMessage(f"❌ 转换错误: {str(e)}")

    def update_timestamp_placeholder(self):
        """根据选择的转换类型更新占位符文本"""
        conversion_type = self.timestamp_type_combo.currentText()
        if conversion_type == "时间戳转时间":
            self.timestamp_entry.clear()
            self.timestamp_entry.setPlaceholderText("请输入Unix时间戳值，例如: 1609459200")
            self.update_status("🟢 请输入Unix时间戳值，例如: 1609459200")
        else:
            self.timestamp_entry.clear()
            self.timestamp_entry.setPlaceholderText("请输入时间，例如: 2021-01-01 00:00:00")
            self.update_status("🟢 请输入时间，例如: 2021-01-01 00:00:00")

        # 重置按钮状态和样式
        self.timestamp_convert_btn.setEnabled(False)
        self.timestamp_entry.setStyleSheet("")

    def set_current_time(self):
        """根据转换类型设置当前时间或时间戳"""
        # 获取转换类型
        conversion_type = self.timestamp_type_combo.currentText()

        if conversion_type == "时间转时间戳":
            # 如果是时间转时间戳，设置当前时间
            now = datetime.datetime.now()
            self.timestamp_entry.setText(now.strftime("%Y-%m-%d %H:%M:%S"))
        else:
            # 如果是时间戳转时间，设置当前时间戳
            current_timestamp = time.time()
            self.timestamp_entry.setText(str(int(current_timestamp)))

        # 转换
        self.convert_timestamp()

    def validate_currency_input(self):
        """验证货币输入是否为有效数字"""
        value = self.usd_entry.text().strip()

        if not value:
            self.currency_convert_btn.setEnabled(False)
            self.usd_entry.setStyleSheet("")
            return

        try:
            # 允许小数输入
            amount = float(value)
            if amount < 0:
                self.update_status("⚠️ 金额不能为负数")
                self.currency_convert_btn.setEnabled(False)
                self.usd_entry.setStyleSheet("border: 2px solid #f44336;")
            else:
                self.update_status("🟢 准备就绪")
                self.currency_convert_btn.setEnabled(True)
                self.usd_entry.setStyleSheet("border: 2px solid #4CAF50;")
        except ValueError:
            self.update_status("⚠️ 请输入有效的数字")
            self.currency_convert_btn.setEnabled(False)
            self.usd_entry.setStyleSheet("border: 2px solid #f44336;")

    def update_manual_rate(self):
        """更新手动设置的汇率"""
        try:
            rate = float(self.rate_entry.text())
            if rate <= 0:
                self.update_status("⚠️ 汇率必须大于0")
                return

            self.exchange_rate = rate
            self.update_status(f"✅ 已手动更新汇率: 1美元 = {rate}人民币")
            self.update_time_label.setText("🕒 手动设置")
            # 如果已有输入，自动转换
            if self.usd_entry.text().strip():
                self.convert_currency()
        except ValueError:
            self.update_status("⚠️ 请输入有效的汇率数字")
            
    def get_exchange_rate(self):
        """获取实时汇率数据（从中国银行官网获取）"""
        self.statusBar().showMessage("🔄 正在获取最新汇率...")

        # 检查是否安装了BeautifulSoup
        if not BS4_AVAILABLE:
            self.statusBar().showMessage("⚠️ 未安装BeautifulSoup库，无法从中国银行网站获取汇率。使用备用数据源...")
            self.get_exchange_rate_backup()
            return

        try:
            # 使用中国银行官网获取美元兑人民币汇率
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }

            # 中国银行外汇牌价网址
            url = "https://www.boc.cn/sourcedb/whpj/enindex_1619.html"

            response = requests.get(url, headers=headers, timeout=10)

            if response.status_code == 200:
                # 使用BeautifulSoup解析HTML
                soup = BeautifulSoup(response.text, 'html.parser')

                # 找到汇率表格
                table = soup.find('table', {'bgcolor': '#EAEAEA'})
                if table:
                    # 查找所有行
                    rows = table.find_all('tr')

                    # 遍历行寻找USD(美元)汇率
                    for row in rows[1:]:  # 跳过表头行
                        cells = row.find_all('td')
                        if len(cells) >= 7:  # 确保行有足够的单元格
                            currency_name = cells[0].text.strip()
                            if currency_name == 'USD':
                                # 获取卖出价作为汇率
                                selling_rate_text = cells[3].text.strip()
                                if selling_rate_text and selling_rate_text != '--':
                                    try:
                                        selling_rate = float(selling_rate_text)
                                        # 中行牌价是按照100外币单位显示的，需要除以100
                                        self.exchange_rate = selling_rate / 100
                                        self.rate_entry.setText(str(round(self.exchange_rate, 4)))
                                        self.last_rate_update = datetime.datetime.now()

                                        # 更新状态和时间显示
                                        self.statusBar().showMessage(f"✅ 汇率更新成功: 1美元 = {self.exchange_rate}人民币")
                                        self.update_time_label.setText(f"🕒 更新于: {self.last_rate_update.strftime('%Y-%m-%d %H:%M')}")

                                        # 如果已有输入，自动转换
                                        if self.usd_entry.text().strip():
                                            self.convert_currency()
                                        return
                                    except ValueError:
                                        self.statusBar().showMessage(f"❌ 解析汇率数据失败，数值格式错误: {selling_rate_text}")
                
                # 尝试使用中间价(Middle Rate)
                table = soup.find('table', {'bgcolor': '#EAEAEA'})
                if table:
                    for row in table.find_all('tr')[1:]:
                        cells = row.find_all('td')
                        if len(cells) >= 7:
                            currency_name = cells[0].text.strip()
                            if currency_name == 'USD':
                                middle_rate_text = cells[5].text.strip()
                                if middle_rate_text and middle_rate_text != '--':
                                    try:
                                        middle_rate = float(middle_rate_text)
                                        self.exchange_rate = middle_rate / 100
                                        self.rate_entry.setText(str(round(self.exchange_rate, 4)))
                                        self.last_rate_update = datetime.datetime.now()

                                        self.statusBar().showMessage(f"✅ 汇率更新成功(使用中间价): 1美元 = {self.exchange_rate}人民币")
                                        self.update_time_label.setText(f"🕒 更新于: {self.last_rate_update.strftime('%Y-%m-%d %H:%M')}")

                                        if self.usd_entry.text().strip():
                                            self.convert_currency()
                                        return
                                    except ValueError:
                                        pass

                # 如果未找到USD汇率或解析失败，尝试备用方法
                self.statusBar().showMessage("⚠️ 未在中国银行网站找到美元汇率，使用备用数据源...")
                self.get_exchange_rate_backup()
            else:
                # 如果请求失败，尝试备用方法
                self.statusBar().showMessage("⚠️ 无法连接到中国银行网站，使用备用数据源...")
                self.get_exchange_rate_backup()
        except Exception as e:
            self.statusBar().showMessage(f"❌ 获取汇率失败: {str(e)}")
            # 尝试备用方法
            self.get_exchange_rate_backup()
            
    def get_exchange_rate_backup(self):
        """备用方法获取汇率（使用开放API）"""
        try:
            # 首先尝试使用第一个备用API
            response = requests.get("https://open.er-api.com/v6/latest/USD", timeout=10)
            data = response.json()

            if response.status_code == 200 and "rates" in data and "CNY" in data["rates"]:
                self.exchange_rate = data["rates"]["CNY"]
                self.rate_entry.setText(str(round(self.exchange_rate, 4)))
                self.last_rate_update = datetime.datetime.now()

                # 更新状态和时间显示
                self.statusBar().showMessage(f"✅ 汇率更新成功(备用源1): 1美元 = {self.exchange_rate}人民币")
                self.update_time_label.setText(f"🕒 更新于: {self.last_rate_update.strftime('%Y-%m-%d %H:%M')}")

                # 如果已有输入，自动转换
                if self.usd_entry.text().strip():
                    self.convert_currency()
                return

            # 如果第一个备用API失败，尝试第二个备用API
            self.statusBar().showMessage("🔄 正在尝试第二个备用数据源...")
            response = requests.get("https://api.exchangerate-api.com/v4/latest/USD", timeout=10)
            data = response.json()

            if response.status_code == 200 and "rates" in data and "CNY" in data["rates"]:
                self.exchange_rate = data["rates"]["CNY"]
                self.rate_entry.setText(str(round(self.exchange_rate, 4)))
                self.last_rate_update = datetime.datetime.now()

                # 更新状态和时间显示
                self.statusBar().showMessage(f"✅ 汇率更新成功(备用源2): 1美元 = {self.exchange_rate}人民币")
                self.update_time_label.setText(f"🕒 更新于: {self.last_rate_update.strftime('%Y-%m-%d %H:%M')}")

                # 如果已有输入，自动转换
                if self.usd_entry.text().strip():
                    self.convert_currency()
                return

            # 所有API都失败，提示用户手动设置
            self.statusBar().showMessage("⚠️ 无法获取汇率数据，请手动设置汇率或稍后再试")
        except Exception as e:
            self.statusBar().showMessage(f"❌ 获取汇率失败: {str(e)}，请手动设置汇率")
            
    def convert_currency(self):
        """将美元转换为人民币"""
        value = self.usd_entry.text().strip()
        if not value:
            return

        try:
            # 获取美元金额
            usd_amount = float(value)

            # 转换为人民币
            cny_amount = usd_amount * self.exchange_rate

            # 格式化显示
            usd_formatted = f"${usd_amount:,.2f}"
            cny_formatted = f"¥{cny_amount:,.2f}"

            # 构建结果文本
            result_text = f"💰 货币汇率转换结果\n\n"
            result_text += f"输入金额: {usd_formatted} USD\n"
            result_text += f"当前汇率: {self.exchange_rate:.4f} CNY/USD\n\n"
            result_text += "转换结果:\n"
            result_text += "=" * 40 + "\n"
            result_text += f"💵 美元金额: {usd_formatted}\n"
            result_text += f"💴 人民币金额: {cny_formatted}\n\n"
            result_text += "计算详情:\n"
            result_text += "=" * 40 + "\n"
            result_text += f"计算公式: {usd_amount} × {self.exchange_rate:.4f} = {cny_amount:.2f}\n"
            result_text += f"汇率更新时间: {self.update_time_label.text()}\n"

            # 更新右侧结果展示面板
            self.result_display.clear()
            self.result_display.setPlainText(result_text)

            # 更新状态栏
            self.statusBar().showMessage(f"✅ 已将 {usd_formatted} 转换为 {cny_formatted}")

        except ValueError:
            self.statusBar().showMessage("❌ 请输入有效的数字")
        except Exception as e:
            self.statusBar().showMessage(f"❌ 转换错误: {str(e)}")

def main():
    """主函数"""
    app = QApplication(sys.argv)
    window = ConverterApp()
    window.show()
    sys.exit(app.exec())

if __name__ == "__main__":
    main()


